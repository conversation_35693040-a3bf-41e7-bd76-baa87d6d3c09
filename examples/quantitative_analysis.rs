use candle_bert_time_series::dataset::load_and_prepare_data;
use candle_bert_time_series::backtest::{extract_test_split, Backtester, TradingFees, TradeSide, PerformanceMetrics};
use candle_core::{Device, Result, Tensor, DType};
use candle_nn::{<PERSON><PERSON><PERSON><PERSON><PERSON>, VarMap};
use std::collections::HashMap;

// Include the financial_bert module
#[path = "../src/financial_bert.rs"]
mod financial_bert;
use financial_bert::{Config, FinancialTransformerForMaskedRegression};

/// Quantitative Analysis Tool for Cross-Sectional Crypto Inference
///
/// This analysis focuses on the model's ability to infer one currency's movements
/// based on the movements of other currencies, rather than next-step prediction.
///
/// Strategy:
/// 1. Select a subset of cryptocurrencies to "black out" (hide from model)
/// 2. Train model to predict these blacked-out cryptos using only the others
/// 3. Measure model divergence vs actual returns
/// 4. Trade based on divergence signals rather than absolute predictions

const SEQUENCE_LENGTH: usize = 120;
const MODEL_DIMS: usize = 256;
const NUM_LAYERS: usize = 6;
const NUM_HEADS: usize = 8;

struct CrossSectionalAnalyzer {
    model: FinancialTransformerForMaskedRegression,
    device: Device,
    blacked_out_indices: Vec<usize>,  // Cryptos to predict
    predictor_indices: Vec<usize>,    // Cryptos to use as predictors
}

impl CrossSectionalAnalyzer {
    fn new(
        model: FinancialTransformerForMaskedRegression, 
        device: Device,
        num_assets: usize,
        num_blacked_out: usize,
    ) -> Self {
        // Select cryptos to black out (predict) - choose diverse indices
        let mut blacked_out_indices = Vec::new();
        let step = num_assets / num_blacked_out;
        for i in 0..num_blacked_out {
            blacked_out_indices.push((i * step).min(num_assets - 1));
        }
        
        // Remaining cryptos are predictors
        let predictor_indices: Vec<usize> = (0..num_assets)
            .filter(|&i| !blacked_out_indices.contains(&i))
            .collect();
        
        println!("🎯 Cross-sectional setup:");
        println!("  - Blacked out cryptos (to predict): {:?}", blacked_out_indices);
        println!("  - Predictor cryptos: {} assets", predictor_indices.len());
        
        Self { 
            model, 
            device, 
            blacked_out_indices, 
            predictor_indices 
        }
    }

    /// Create masked input where blacked-out cryptos are set to zero
    fn create_masked_input(&self, data: &Tensor, timestamp: usize) -> Result<Tensor> {
        if timestamp < SEQUENCE_LENGTH {
            return Err(candle_core::Error::Msg("Not enough history for sequence".to_string()));
        }

        let start_idx = timestamp - SEQUENCE_LENGTH;
        let input_sequence = data.narrow(0, start_idx, SEQUENCE_LENGTH)?;
        let mut masked_sequence = input_sequence.clone();

        // Zero out the blacked-out cryptocurrencies
        for &crypto_idx in &self.blacked_out_indices {
            let zeros = Tensor::zeros((SEQUENCE_LENGTH,), DType::F32, &self.device)?;
            masked_sequence = masked_sequence.slice_assign(&[.., crypto_idx..crypto_idx+1], &zeros.unsqueeze(1)?)?;
        }

        Ok(masked_sequence)
    }

    /// Get cross-sectional predictions for blacked-out cryptos
    fn get_cross_sectional_prediction(&self, data: &Tensor, timestamp: usize) -> Result<Vec<f64>> {
        let masked_input = self.create_masked_input(data, timestamp)?;
        let input_batch = masked_input.unsqueeze(0)?; // Add batch dimension

        // Get model predictions
        let predictions = self.model.forward(&input_batch)?;
        
        // Extract predictions for the last timestep
        let last_timestep_predictions = predictions.get(0)?.get(SEQUENCE_LENGTH - 1)?;
        let predictions_vec: Vec<f32> = last_timestep_predictions.to_vec1()?;
        
        // Return only predictions for blacked-out cryptos
        let mut blacked_out_predictions = Vec::new();
        for &idx in &self.blacked_out_indices {
            blacked_out_predictions.push(predictions_vec[idx] as f64);
        }
        
        Ok(blacked_out_predictions)
    }

    /// Calculate model divergence: difference between predicted and actual returns
    fn calculate_divergence(&self, data: &Tensor, start_time: usize, end_time: usize) -> Result<Vec<Vec<f64>>> {
        let mut divergences = Vec::new();
        
        for timestamp in start_time..end_time {
            if timestamp < SEQUENCE_LENGTH {
                continue;
            }
            
            // Get model predictions for blacked-out cryptos
            let predictions = self.get_cross_sectional_prediction(data, timestamp)?;
            
            // Get actual returns for blacked-out cryptos
            let actual_returns_row = data.get(timestamp)?;
            let actual_returns_vec: Vec<f32> = actual_returns_row.to_vec1()?;
            
            let mut timestamp_divergences = Vec::new();
            for (i, &crypto_idx) in self.blacked_out_indices.iter().enumerate() {
                let actual = actual_returns_vec[crypto_idx] as f64;
                let predicted = predictions[i];
                let divergence = predicted - actual; // Positive = model overestimated
                timestamp_divergences.push(divergence);
            }
            
            divergences.push(timestamp_divergences);
        }
        
        Ok(divergences)
    }

    /// Analyze cross-sectional inference quality
    fn analyze_inference_quality(&self, data: &Tensor, start_time: usize, end_time: usize) -> Result<()> {
        println!("\n🔍 CROSS-SECTIONAL INFERENCE ANALYSIS");
        println!("======================================================================");
        
        let divergences = self.calculate_divergence(data, start_time, end_time)?;
        
        if divergences.is_empty() {
            println!("❌ No divergence data available");
            return Ok(());
        }
        
        // Calculate statistics for each blacked-out crypto
        for (crypto_i, &crypto_idx) in self.blacked_out_indices.iter().enumerate() {
            let crypto_divergences: Vec<f64> = divergences.iter()
                .map(|div| div[crypto_i])
                .collect();
            
            let mean_divergence = crypto_divergences.iter().sum::<f64>() / crypto_divergences.len() as f64;
            let variance = crypto_divergences.iter()
                .map(|x| (x - mean_divergence).powi(2))
                .sum::<f64>() / crypto_divergences.len() as f64;
            let std_dev = variance.sqrt();
            
            // Calculate correlation between predictions and actuals
            let mut predictions = Vec::new();
            let mut actuals = Vec::new();
            
            for timestamp in start_time..end_time {
                if timestamp < SEQUENCE_LENGTH {
                    continue;
                }
                
                if let Ok(preds) = self.get_cross_sectional_prediction(data, timestamp) {
                    if let Ok(actual_row) = data.get(timestamp) {
                        if let Ok(actual_vec) = actual_row.to_vec1::<f32>() {
                            predictions.push(preds[crypto_i]);
                            actuals.push(actual_vec[crypto_idx] as f64);
                        }
                    }
                }
            }
            
            let correlation = self.calculate_correlation(&predictions, &actuals);
            
            println!("\n📊 CRYPTO_{} (Index: {}) Inference Quality:", crypto_idx, crypto_idx);
            println!("  - Mean divergence: {:.6}", mean_divergence);
            println!("  - Std deviation: {:.6}", std_dev);
            println!("  - Correlation: {:.4}", correlation);
            println!("  - Data points: {}", crypto_divergences.len());
            
            // Interpretation
            if correlation.abs() > 0.1 {
                println!("  ✅ Strong cross-sectional signal");
            } else if correlation.abs() > 0.05 {
                println!("  ⚠️  Weak cross-sectional signal");
            } else {
                println!("  ❌ No meaningful cross-sectional relationship");
            }
        }
        
        Ok(())
    }

    /// Calculate Pearson correlation coefficient
    fn calculate_correlation(&self, x: &[f64], y: &[f64]) -> f64 {
        if x.len() != y.len() || x.is_empty() {
            return 0.0;
        }
        
        let n = x.len() as f64;
        let sum_x: f64 = x.iter().sum();
        let sum_y: f64 = y.iter().sum();
        let sum_xy: f64 = x.iter().zip(y.iter()).map(|(a, b)| a * b).sum();
        let sum_x2: f64 = x.iter().map(|a| a * a).sum();
        let sum_y2: f64 = y.iter().map(|b| b * b).sum();
        
        let numerator = n * sum_xy - sum_x * sum_y;
        let denominator = ((n * sum_x2 - sum_x * sum_x) * (n * sum_y2 - sum_y * sum_y)).sqrt();
        
        if denominator == 0.0 {
            0.0
        } else {
            numerator / denominator
        }
    }
}

/// Divergence-based trading strategy
struct DivergenceStrategy {
    analyzer: CrossSectionalAnalyzer,
    divergence_threshold: f64,  // Trade when |divergence| > threshold
    position_size: f64,         // Fraction of portfolio per position
}

impl DivergenceStrategy {
    fn new(analyzer: CrossSectionalAnalyzer, divergence_threshold: f64, position_size: f64) -> Self {
        Self {
            analyzer,
            divergence_threshold,
            position_size,
        }
    }

    /// Generate trading signals based on model divergence
    /// Positive divergence (model overestimated) -> Short signal
    /// Negative divergence (model underestimated) -> Long signal
    fn generate_signals(&self, data: &Tensor, timestamp: usize) -> Result<Vec<(usize, TradeSide, f64)>> {
        let mut signals = Vec::new();
        
        if timestamp < SEQUENCE_LENGTH + 1 {
            return Ok(signals);
        }
        
        // Get current divergence
        let predictions = self.analyzer.get_cross_sectional_prediction(data, timestamp)?;
        let actual_returns_row = data.get(timestamp - 1)?; // Previous period's actual returns
        let actual_returns_vec: Vec<f32> = actual_returns_row.to_vec1()?;
        
        for (i, &crypto_idx) in self.analyzer.blacked_out_indices.iter().enumerate() {
            let actual = actual_returns_vec[crypto_idx] as f64;
            let predicted = predictions[i];
            let divergence = predicted - actual;
            
            // Trade on significant divergence
            if divergence.abs() > self.divergence_threshold {
                let side = if divergence > 0.0 {
                    TradeSide::Sell  // Model overestimated, expect reversion
                } else {
                    TradeSide::Buy   // Model underestimated, expect catch-up
                };
                
                signals.push((crypto_idx, side, self.position_size));
            }
        }
        
        Ok(signals)
    }
}

fn main() -> Result<()> {
    println!("📊 QUANTITATIVE ANALYSIS - Cross-Sectional Crypto Inference");
    println!("======================================================================");
    println!("This analysis focuses on inferring currency movements from others,");
    println!("not next-step prediction. Trading based on model divergence signals.");
    println!("======================================================================");

    // Setup device
    let device = Device::cuda_if_available(0)?;
    println!("Using device: {:?}", device);

    // Configuration
    let data_path = "/home/<USER>/Downloads/transformed_dataset.parquet";
    let model_path = "current_model.safetensors";
    let initial_capital = 100000.0;

    // Load data
    println!("\nLoading cryptocurrency data...");
    let (full_data_sequence, num_time_series) = load_and_prepare_data(data_path, &device)?;
    
    // Extract ONLY the test split to prevent data leakage
    let test_data = extract_test_split(&full_data_sequence)?;
    let test_timesteps = test_data.dims()[0];
    
    println!("Data loaded: {} assets, {} test timesteps", num_time_series, test_timesteps);

    // Load trained model
    println!("\n🤖 Loading trained model...");
    let config = Config {
        num_time_series,
        hidden_size: MODEL_DIMS,
        num_hidden_layers: NUM_LAYERS,
        num_attention_heads: NUM_HEADS,
        intermediate_size: MODEL_DIMS * 4,
        hidden_act: financial_bert::HiddenAct::Gelu,
        hidden_dropout_prob: 0.1,
        max_position_embeddings: SEQUENCE_LENGTH,
        initializer_range: 0.02,
        layer_norm_eps: 1e-12,
        position_embedding_type: financial_bert::PositionEmbeddingType::Absolute,
        use_cache: false,
        model_type: Some("financial_transformer".to_string()),
    };

    let mut varmap = VarMap::new();
    let vb = VarBuilder::from_varmap(&varmap, DType::F32, &device);
    let model = FinancialTransformerForMaskedRegression::load(vb, &config)?;
    varmap.load(model_path)?;
    println!("✅ Model loaded from: {}", model_path);

    // Initialize cross-sectional analyzer
    let num_blacked_out = (num_time_series / 4).max(3).min(8); // 25% of cryptos, 3-8 range
    let analyzer = CrossSectionalAnalyzer::new(model, device.clone(), num_time_series, num_blacked_out);

    // Analyze inference quality
    let analysis_start = test_timesteps / 4;
    let analysis_end = (analysis_start + 5000).min(test_timesteps - 1);
    
    analyzer.analyze_inference_quality(&test_data, analysis_start, analysis_end)?;

    // Initialize divergence-based trading strategy
    println!("\n💰 DIVERGENCE-BASED TRADING STRATEGY");
    println!("======================================================================");

    let divergence_threshold = 0.01; // 1% divergence threshold
    let position_size = 0.1; // 10% of portfolio per position
    let strategy = DivergenceStrategy::new(analyzer, divergence_threshold, position_size);

    // Create symbol names for backtesting
    let symbol_names: Vec<String> = (0..num_time_series)
        .map(|i| format!("CRYPTO_{}", i))
        .collect();

    // Initialize backtester
    let fees = TradingFees::default();
    let mut backtester = Backtester::new(
        initial_capital,
        test_data.clone(),
        symbol_names.clone(),
        Some(fees),
    )?;

    println!("🚀 Running divergence-based backtest...");
    println!("  - Divergence threshold: {:.2}%", divergence_threshold * 100.0);
    println!("  - Position size: {:.1}%", position_size * 100.0);
    println!("  - Trading period: {} to {} ({} timesteps)",
             analysis_start, analysis_end, analysis_end - analysis_start);

    let mut total_trades = 0;
    let mut successful_trades = 0;

    // Run backtest
    for timestamp in analysis_start..analysis_end {
        // Step forward to update prices
        backtester.step_forward(timestamp)?;

        // Generate trading signals based on divergence
        if let Ok(signals) = strategy.generate_signals(&test_data, timestamp) {
            for (crypto_idx, side, size) in signals {
                let symbol = &symbol_names[crypto_idx];

                // Calculate position size in shares
                let current_portfolio_value = backtester.portfolio_history.last().unwrap().total_value;
                let position_value = current_portfolio_value * size;
                let current_price = backtester.current_prices[crypto_idx];
                let shares = position_value / current_price;

                // Execute trade
                if let Ok(_) = backtester.execute_trade(symbol, side, shares, timestamp) {
                    total_trades += 1;

                    // Check if this trade will be profitable (simplified check)
                    // In practice, you'd track this over multiple periods
                    if timestamp + 5 < test_timesteps {
                        let future_return = test_data.get(timestamp + 4)?.get(crypto_idx)?.to_scalar::<f32>()? as f64;
                        let expected_profit = match side {
                            TradeSide::Buy => future_return > 0.0,
                            TradeSide::Sell => future_return < 0.0,
                        };
                        if expected_profit {
                            successful_trades += 1;
                        }
                    }
                }
            }
        }

        // Print progress every 1000 timesteps
        if timestamp % 1000 == 0 {
            let current_value = backtester.portfolio_history.last().unwrap().total_value;
            let return_pct = (current_value - initial_capital) / initial_capital * 100.0;
            println!("  Timestamp {}: Portfolio value: ${:.2} ({:+.2}%)",
                     timestamp, current_value, return_pct);
        }
    }

    // Calculate final performance metrics
    let metrics = backtester.calculate_metrics()?;

    println!("\n📈 BACKTEST RESULTS");
    println!("======================================================================");
    println!("💰 Financial Performance:");
    println!("  - Initial capital: ${:.2}", initial_capital);
    println!("  - Final value: ${:.2}", metrics.final_value);
    println!("  - Total return: {:.2}%", metrics.total_return * 100.0);
    println!("  - Sharpe ratio: {:.3}", metrics.sharpe_ratio);
    println!("  - Max drawdown: {:.2}%", metrics.max_drawdown * 100.0);

    println!("\n📊 Trading Statistics:");
    println!("  - Total trades: {}", total_trades);
    println!("  - Successful trades: {} ({:.1}%)",
             successful_trades,
             if total_trades > 0 { successful_trades as f64 / total_trades as f64 * 100.0 } else { 0.0 });
    println!("  - Total fees paid: ${:.2}", metrics.total_fees);

    println!("\n🎯 Strategy Interpretation:");
    if metrics.total_return > 0.05 {
        println!("  ✅ Strong performance: Divergence signals are profitable");
    } else if metrics.total_return > 0.0 {
        println!("  ⚠️  Modest performance: Some signal but room for improvement");
    } else {
        println!("  ❌ Poor performance: Divergence signals may not be reliable");
    }

    if metrics.sharpe_ratio > 1.0 {
        println!("  ✅ Excellent risk-adjusted returns");
    } else if metrics.sharpe_ratio > 0.5 {
        println!("  ⚠️  Decent risk-adjusted returns");
    } else {
        println!("  ❌ Poor risk-adjusted returns");
    }

    println!("\n💡 STRATEGY INSIGHTS:");
    println!("======================================================================");
    println!("This strategy trades on model divergence rather than predictions:");
    println!("  - When model overestimates returns → Short (expect reversion)");
    println!("  - When model underestimates returns → Long (expect catch-up)");
    println!("  - Success depends on cross-sectional relationships being stable");
    println!("  - Works best when model captures relative dynamics between cryptos");

    println!("\n✅ Quantitative analysis complete!");

    Ok(())
}
